{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B1A26FA389F782B3FF882853410296A33F747CF0F79A0105B419CC659105E9B99775E1DB94F97EE",
          "profile": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.p12",
          "storePassword": "0000001B6C4FA08FABB57B4D6B65F73446B5F363741399BB669F5F586956A3E87638E443CB643254C3CA03"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "profile"
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}